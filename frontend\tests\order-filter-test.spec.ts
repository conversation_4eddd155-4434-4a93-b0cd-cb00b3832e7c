import { expect, test } from '@playwright/test'

test.describe('订单筛选功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 准备：登录到系统
    await page.goto('/')

    // 等待登录页面加载
    await expect(page).toHaveURL('/login', { timeout: 10000 })

    // 填写登录信息（使用正确的测试账号）
    await page.getByPlaceholder('请输入用户名').fill('<EMAIL>')
    await page.getByPlaceholder('请输入密码').fill('1C#\\r4@%kL}8Zhy)]#')
    await page.getByRole('button', { name: '登录' }).click()

    // 等待登录成功并导航到订单管理页面
    await page.waitForURL('**/visa-form', { timeout: 10000 })
    await page.goto('/order-management')

    // 等待订单页面加载
    await expect(page.locator('h2').getByText('我的订单')).toBeVisible({ timeout: 15000 })
  })

  test('客户筛选功能测试', async ({ page }) => {
    console.log('🔍 测试客户来源筛选功能...')

    // 查找客户来源筛选输入框
    const customerSourceInput = page
      .locator('input[placeholder*="客户来源"]')
      .or(page.locator('input').filter({ hasText: 'customer_source' }))
      .or(
        page.locator('.el-input__inner').nth(0), // 假设是第一个输入框
      )

    // 检查客户筛选输入框是否存在
    await expect(customerSourceInput).toBeVisible({ timeout: 5000 })

    // 输入测试数据
    await customerSourceInput.fill('飞猪')

    // 点击查询按钮
    const searchButton = page.locator('button').filter({ hasText: '查询' })
    await expect(searchButton).toBeVisible()
    await searchButton.click()

    // 等待查询结果加载
    await page.waitForTimeout(2000)

    // 验证筛选是否工作
    // 检查是否有"无数据"提示或者有筛选结果
    const noDataElement = page.locator('.el-empty').or(page.locator('text=暂无数据'))
    const tableRows = page.locator('.el-table__row')

    const hasNoData = await noDataElement.isVisible().catch(() => false)
    const hasData = await tableRows
      .count()
      .then((count) => count > 0)
      .catch(() => false)

    if (hasNoData) {
      console.log('✅ 客户筛选功能正常 - 无匹配数据')
    } else if (hasData) {
      console.log('✅ 客户筛选功能正常 - 找到匹配数据')
    } else {
      console.log('⚠️ 客户筛选状态不明确')
    }

    // 清除筛选条件
    await customerSourceInput.clear()
    const resetButton = page.locator('button').filter({ hasText: '重置' })
    if (await resetButton.isVisible().catch(() => false)) {
      await resetButton.click()
    }
  })

  test('申请人姓名筛选功能测试', async ({ page }) => {
    console.log('🔍 测试申请人姓名筛选功能...')

    // 查找申请人姓名筛选输入框
    const applicantNameInput = page
      .locator('input[placeholder*="申请人姓名"]')
      .or(page.locator('input[placeholder*="姓名"]'))
      .or(
        page.locator('.el-input__inner').nth(1), // 假设是第二个输入框
      )

    // 检查姓名筛选输入框是否存在
    await expect(applicantNameInput).toBeVisible({ timeout: 5000 })

    // 输入测试数据
    await applicantNameInput.fill('张三')

    // 点击查询按钮
    const searchButton = page.locator('button').filter({ hasText: '查询' })
    await expect(searchButton).toBeVisible()
    await searchButton.click()

    // 等待查询结果加载
    await page.waitForTimeout(2000)

    // 验证筛选是否工作
    const noDataElement = page.locator('.el-empty').or(page.locator('text=暂无数据'))
    const tableRows = page.locator('.el-table__row')

    const hasNoData = await noDataElement.isVisible().catch(() => false)
    const hasData = await tableRows
      .count()
      .then((count) => count > 0)
      .catch(() => false)

    if (hasNoData) {
      console.log('✅ 姓名筛选功能正常 - 无匹配数据')
    } else if (hasData) {
      console.log('✅ 姓名筛选功能正常 - 找到匹配数据')

      // 检查结果中是否包含搜索的姓名
      const tableContent = await page.locator('.el-table').textContent()
      if (tableContent?.includes('张三')) {
        console.log('✅ 姓名筛选结果准确')
      } else {
        console.log('⚠️ 姓名筛选结果可能不准确')
      }
    } else {
      console.log('⚠️ 姓名筛选状态不明确')
    }
  })

  test('组合筛选功能测试', async ({ page }) => {
    console.log('🔍 测试组合筛选功能...')

    // 同时使用客户来源和姓名筛选
    const customerSourceInput = page.locator('.el-input__inner').nth(0)
    const applicantNameInput = page.locator('.el-input__inner').nth(1)

    await customerSourceInput.fill('飞猪')
    await applicantNameInput.fill('李四')

    // 点击查询
    const searchButton = page.locator('button').filter({ hasText: '查询' })
    await searchButton.click()

    // 等待结果
    await page.waitForTimeout(2000)

    console.log('✅ 组合筛选测试完成')
  })

  test('筛选状态保持测试', async ({ page }) => {
    console.log('🔍 测试筛选状态保持...')

    // 设置筛选条件
    const customerSourceInput = page.locator('.el-input__inner').nth(0)
    await customerSourceInput.fill('测试客户')

    // 查询
    const searchButton = page.locator('button').filter({ hasText: '查询' })
    await searchButton.click()
    await page.waitForTimeout(1000)

    // 检查筛选条件是否保持
    const inputValue = await customerSourceInput.inputValue()
    expect(inputValue).toBe('测试客户')

    console.log('✅ 筛选状态保持测试完成')
  })

  test('重置功能测试', async ({ page }) => {
    console.log('🔍 测试重置功能...')

    // 填写筛选条件
    const customerSourceInput = page.locator('.el-input__inner').nth(0)
    const applicantNameInput = page.locator('.el-input__inner').nth(1)

    await customerSourceInput.fill('测试客户')
    await applicantNameInput.fill('测试姓名')

    // 点击重置按钮
    const resetButton = page.locator('button').filter({ hasText: '重置' })
    await expect(resetButton).toBeVisible()
    await resetButton.click()

    // 验证所有输入框都被清空
    await expect(customerSourceInput).toHaveValue('')
    await expect(applicantNameInput).toHaveValue('')

    console.log('✅ 重置功能测试完成')
  })
})
