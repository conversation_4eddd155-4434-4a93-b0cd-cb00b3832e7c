import { getUserInfoApi, loginApi, logoutApi } from '@/api/auth'
import type { LoginRequest, User } from '@/types/auth'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { useNotificationStore } from './notification'

export const useAuthStore = defineStore('auth', () => {
  // 🔧 修复：使用统一的通知系统
  const notificationStore = useNotificationStore()

  // 状态 - 🔧 修复：移除手动localStorage读取，让persist插件处理
  const token = ref<string | null>(null)
  const user = ref<User | null>(null)
  const isLoading = ref(false)
  // 🔧 新增：避免重复Token检查的标记
  const isTokenChecked = ref(false)
  // 🔥 新增：会话失效状态（单设备登录）
  const isSessionInvalidated = ref(false)

  // 计算属性 - 🔧 修复：优化认证判断逻辑
  const isAuthenticated = computed(() => {
    // 如果有token，认为用户已认证（即使user信息还在获取中）
    if (token.value) {
      return true
    }
    return false
  })

  // 登录方法
  const login = async (loginData: LoginRequest): Promise<boolean> => {
    if (isLoading.value) return false

    isLoading.value = true
    notificationStore.clearMessages()

    try {
      const response = await loginApi(loginData)

      if (response.success) {
        token.value = response.data.token
        user.value = response.data.user

        // 🔥 安全修复：登录成功后立即清理localStorage中的应用数据
        // 防止加载其他用户的数据（跨用户数据泄露防护）
        try {
          const { useApplicationStore } = await import('@/stores/application')
          const applicationStore = useApplicationStore()
          applicationStore.clearApplications()
          console.log('🔒 登录安全检查：已清理localStorage中的应用数据')

          // 🔧 完全清理：移除可能的其他用户相关数据
          const keysToClean = [
            'visa_applications',
            'visa_form_snapshot',
            'form_persistence_data',
            'user_preferences',
          ]

          keysToClean.forEach((key) => {
            if (localStorage.getItem(key)) {
              localStorage.removeItem(key)
              console.log(`🔒 已清理localStorage键: ${key}`)
            }
          })
        } catch (error) {
          console.warn('清理应用数据失败:', error)
        }

        // 记住登录状态（如果选择记住）
        if (loginData.remember) {
          localStorage.setItem('remembered_username', loginData.username)
          localStorage.setItem('remember_me', 'true')
        } else {
          clearRememberedCredentials()
        }

        // 🔥 二层检测：登录成功后启动智能会话检测
        try {
          const { useSessionStore } = await import('@/stores/session')
          const sessionStore = useSessionStore()
          sessionStore.startSessionDetection()
          console.log('🔥 登录成功，二层检测机制已启动')
        } catch (error) {
          console.error('启动两层检测机制失败:', error)
        }

        // 🔧 修复：使用统一通知系统
        notificationStore.showSuccess('登录成功！')

        return true
      } else {
        // 🔧 修复：使用统一通知系统
        notificationStore.showError(response.message || '登录失败')
        return false
      }
    } catch (error) {
      console.error('💥 登录异常:', error)
      // 🔧 修复：使用统一通知系统
      notificationStore.showError(error instanceof Error ? error.message : '登录过程中发生未知错误')
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 获取用户信息
  const getUserInfo = async (): Promise<boolean> => {
    if (!token.value) return false

    try {
      const response = await getUserInfoApi()

      if (response.success) {
        user.value = response.data
        return true
      } else {
        // Token可能已过期，清除认证信息
        await logout()
        // 🔧 修复：使用统一通知系统
        notificationStore.showAuthError('用户信息获取失败，请重新登录')
        return false
      }
    } catch (error) {
      console.error('💥 获取用户信息异常:', error)
      await logout()
      // 🔧 修复：使用统一通知系统
      notificationStore.showAuthError('获取用户信息失败，请重新登录')
      return false
    }
  }

  // 登出方法
  const logout = async (): Promise<void> => {
    // 🚪 设置退出登录状态，避免触发会话检测
    try {
      const { useSessionStore } = await import('@/stores/session')
      const sessionStore = useSessionStore()
      sessionStore.setLoggingOut(true)
      console.log('🚪 开始退出登录流程')
    } catch (error) {
      console.error('设置退出状态失败:', error)
    }

    try {
      if (token.value) {
        await logoutApi()
      }
    } catch (error) {
      console.error('登出API调用失败:', error)
    } finally {
      // 🔧 修复：只清除状态，让persist插件自动处理localStorage
      token.value = null
      user.value = null
      notificationStore.clearMessages() // 清除消息状态

      // 🔥 两层检测：停止所有会话检测机制
      try {
        const { useSessionStore } = await import('@/stores/session')
        const sessionStore = useSessionStore()
        sessionStore.reset()
        console.log('🔥 两层检测机制已停止')
      } catch (error) {
        console.error('停止两层检测机制失败:', error)
      }

      // 🔧 修复：登出时清空应用历史记录
      try {
        const { useApplicationStore } = await import('@/stores/application')
        const applicationStore = useApplicationStore()
        applicationStore.clearApplications()
      } catch {
        // 静默处理错误
      }
    }
  }

  // 检查Token有效性
  const checkTokenValidity = async (): Promise<boolean> => {
    if (!token.value) {
      isTokenChecked.value = true
      return false
    }

    // 🔧 优化：如果已有用户信息且token已检查，直接返回true
    if (isTokenChecked.value && user.value) {
      return true
    }

    // 🔧 页面刷新优化：如果有token但没有用户信息，立即获取用户信息
    if (!user.value) {
      const success = await getUserInfo()
      isTokenChecked.value = true
      return success
    }

    // token和用户信息都存在
    isTokenChecked.value = true
    return true
  }

  // 🔥 设置会话失效状态（单设备登录）
  const setSessionInvalidated = (): void => {
    isSessionInvalidated.value = true
  }

  // 清除认证信息（用于Token失效时）
  const clearAuth = (): void => {
    token.value = null
    user.value = null
    isTokenChecked.value = false // 🔧 重置Token检查状态
    isSessionInvalidated.value = false // 🔥 重置会话失效状态
    notificationStore.clearMessages() // 清除消息状态

    // 🔥 两层检测：停止所有会话检测机制
    try {
      import('@/stores/session').then(({ useSessionStore }) => {
        const sessionStore = useSessionStore()
        sessionStore.reset()
      })
    } catch (error) {
      console.error('停止两层检测机制失败:', error)
    }
  }

  // 获取记住的登录信息（不包含密码，提升安全性）
  const getRememberedCredentials = (): {
    username: string
    remember: boolean
  } | null => {
    const rememberMe = localStorage.getItem('remember_me') === 'true'
    if (rememberMe) {
      const username = localStorage.getItem('remembered_username') ?? ''
      if (username) {
        return { username, remember: true }
      }
    }
    return null
  }

  // 清除记住的登录信息
  const clearRememberedCredentials = (): void => {
    localStorage.removeItem('remembered_username')
    localStorage.removeItem('remember_me')
  }

  // 🔧 新增：初始化时自动恢复用户信息
  const initializeAuth = async () => {
    if (token.value && !user.value && !isTokenChecked.value) {
      const success = await checkTokenValidity()

      // 🔥 二层检测：如果token有效，启动智能会话检测
      if (success) {
        try {
          const { useSessionStore } = await import('@/stores/session')
          const sessionStore = useSessionStore()
          sessionStore.startSessionDetection()
          console.log('🔥 初始化完成，二层检测机制已启动')
        } catch (error) {
          console.error('初始化时启动两层检测机制失败:', error)
        }
      }
    }
  }

  return {
    // 基础状态 - 🔧 直接返回ref让persist插件能监听
    token,
    user,
    isLoading,
    isTokenChecked,
    isSessionInvalidated,

    // 计算属性 - 🔧 保持computed
    isAuthenticated,

    // 方法
    login,
    logout,
    getUserInfo,
    checkTokenValidity,
    clearAuth,
    setSessionInvalidated,
    getRememberedCredentials,
    clearRememberedCredentials,
    initializeAuth,
  }
})
