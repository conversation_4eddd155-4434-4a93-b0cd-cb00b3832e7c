# 文件: app/utils/captcha_solver.py
"""
Anti-Captcha 服务的封装模块

提供验证码识别功能，通过 Anti-Captcha 官方SDK 识别图片中的验证码。
该模块可被应用中的任何组件复用，降低重复代码。

依赖项:
1. anticaptchaofficial - Anti-Captcha官方SDK
   安装: pip install anticaptchaofficial
"""

import os
from time import sleep
from typing import Any

from anticaptchaofficial.imagecaptcha import (
    imagecaptcha,  # type: ignore[import-untyped]
)
from playwright.sync_api import Page

from app.utils.logger_config import get_logger

logger = get_logger()


class CaptchaSolver:
    """
    验证码求解器，封装 Anti-Captcha 官方SDK调用逻辑
    """

    def __init__(
        self, api_key: str | None = None, settings: dict[str, Any] | None = None
    ):
        """
        初始化验证码求解器

        Args:
            api_key: Anti-Captcha API密钥（已废弃，保留参数兼容性）
            settings: 包含配置的字典（已废弃，保留参数兼容性）
        """
        # 只从环境变量获取API密钥
        self.api_key = os.environ.get("ANTI_CAPTCHA_API_KEY")
        if self.api_key:
            logger.info("✅ 从环境变量获取Anti-Captcha API密钥成功")
        else:
            logger.error("❌ 环境变量ANTI_CAPTCHA_API_KEY未设置，验证码识别将失败")

        # 配置参数
        self.max_attempts = 20  # 轮询结果的最大尝试次数
        self.polling_interval = 0.3  # 轮询间隔(秒)

        # 保存上次操作结果信息
        self.last_task_id: int | str | None = None
        self.last_error: str | None = None
        self.last_solution: str | None = None

    def solve_captcha(self, image_path: str, page: Page) -> str | None:
        """
        识别图片中的验证码

        Args:
            image_path: 验证码图片路径
            page: Playwright页面对象（用于检测页面跳转）

        Returns:
            str: 识别结果文本，若失败则返回None
        """
        logger.info(f"尝试识别验证码: {image_path}")
        self.last_error = None
        self.last_solution = None

        if not self.api_key:
            logger.error("❌ 未配置Anti-Captcha API密钥，无法使用在线服务")
            return None

        try:
            # 创建官方SDK实例
            solver = imagecaptcha()
            solver.set_key(self.api_key)
            solver.set_verbose(0)  # 禁用SDK内部日志

            # 配置验证码参数 - 6位数字
            solver.set_numeric(1)  # 仅允许数字
            solver.set_phrase(False)  # 不是短语
            solver.set_case(False)  # 不区分大小写
            solver.set_math(False)  # 不是数学题

            logger.info("🔄 使用官方SDK创建Anti-Captcha任务...")

            # 轮询检查页面跳转和结果
            return self._poll_result_with_sdk(solver, image_path, page)

        except Exception as e:
            self.last_error = str(e)
            logger.error(
                f"❌ Anti-Captcha服务调用失败: {self.last_error}", exc_info=True
            )
            return None

    def _poll_result_with_sdk(
        self, solver: imagecaptcha, image_path: str, page: Page
    ) -> str | None:
        """
        使用官方SDK轮询Anti-Captcha的任务结果，支持页面跳转检测

        Args:
            solver: Anti-Captcha官方SDK实例
            image_path: 验证码图片路径
            page: Playwright页面对象

        Returns:
            str: 识别结果，若失败则返回None
        """
        attempt = 0

        while attempt < self.max_attempts:
            attempt += 1

            # 检查页面是否已跳转（表示验证码已经被处理）
            try:
                if page and any(
                    page.is_visible(sel)
                    for sel in [
                        "text=DECLARATION COMPLETED",
                        "text=Electronic document code",
                        "button:has-text('Confirm')",
                    ]
                ):
                    logger.info("✅ 页面已跳转至 DECLARATION，验证码识别填充完成")
                    return None  # 表示跳过本轮识别，主流程继续
            except Exception as e:
                logger.warning(f"⚠️ 页面跳转检测失败: {e}")

            try:
                logger.info(
                    f"🔄 使用官方SDK识别验证码... 尝试 {attempt}/{self.max_attempts}"
                )

                # 使用官方SDK同步识别
                captcha_text = solver.solve_and_return_solution(image_path)

                if captcha_text == 0:
                    # SDK返回0表示失败
                    self.last_error = solver.error_code or "SDK识别失败"
                    logger.warning(f"⚠️ SDK识别失败: {self.last_error}")
                    sleep(self.polling_interval)
                    continue

                # 验证识别结果是否符合6位数字格式
                if (
                    captcha_text
                    and captcha_text.strip().isdigit()
                    and len(captcha_text.strip()) == 6
                ):
                    result = captcha_text.strip()
                    self.last_solution = result
                    logger.info(f"✅ Anti-Captcha识别成功: {result} (符合6位数字格式)")
                    return result
                else:
                    self.last_error = f"识别结果 '{captcha_text}' 不符合6位数字格式"
                    logger.warning(f"⚠️ {self.last_error}, 重新尝试...")
                    sleep(self.polling_interval)
                    continue

            except Exception as e:
                self.last_error = str(e)
                logger.error(f"❌ SDK识别过程中出错: {self.last_error}")
                sleep(self.polling_interval)
                continue

        self.last_error = f"达到最大尝试次数 ({self.max_attempts})"
        logger.error(f"❌ Anti-Captcha识别超时: {self.last_error}")
        return None

    def get_last_error(self) -> str | None:
        """获取最后一次操作的错误信息"""
        return self.last_error

    def get_last_solution(self) -> str | None:
        """获取最后一次成功的识别结果"""
        return self.last_solution

    def get_last_task_id(self) -> int | str | None:
        """获取最后一次创建的任务ID"""
        return self.last_task_id


# 便捷函数，用于快速调用
def solve_captcha(
    image_path: str,
    page: Page,
    api_key: str | None = None,
    settings: dict[str, Any] | None = None,
) -> str | None:
    """
    快速识别验证码的便捷函数

    Args:
        image_path: 验证码图片路径
        page: Playwright页面对象
        api_key: 已废弃，保留参数兼容性
        settings: 已废弃，保留参数兼容性

    Returns:
        str: 识别结果，若失败则返回None
    """
    solver = CaptchaSolver()
    return solver.solve_captcha(image_path, page)
