"""
测试工具类
=========

提供测试所需的数据库连接、Mock工具和测试数据工厂
"""

import io
from typing import Any
from unittest.mock import AsyncMock, Mock
import uuid
from uuid import uuid4

from faker import Faker
from PIL import Image
import pytest
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine

# Import all necessary models
from app.data.models.applicant import Applicant
from app.data.models.application import Application
from app.data.models.file import File
from app.data.models.order import Order
from app.data.models.user import User
from app.data.models.visa_payment import VisaPayment

fake = Faker()


class ModernizedTestDataGenerator:
    """
    现代化测试数据生成器 - 使用 Faker 库生成高质量的测试数据
    替代手动 TestDataFactory，提供更高质量的测试数据：
    - 使用 Faker 生成真实的姓名、邮箱、电话等
    - 支持参数化测试的数据变化
    - 生成真实的图像数据而不是 fake bytes
    - 返回 SQLAlchemy 模型实例而不是字典
    """

    @staticmethod
    def generate_user_data(**overrides) -> User:
        """生成用户数据 - 返回User模型实例"""
        username = fake.user_name()
        user = User(
            id=uuid.uuid4(),
            username=username,
            email=fake.email(),
            hashed_password=fake.password(length=60),  # 模拟哈希后的密码
            company_name=fake.company(),
            company_address=fake.address(),
            company_contacts=fake.name(),
            phone=fake.phone_number(),
            is_active=True,
            is_verified=True,  # 修复：确保测试稳定性，默认为True
            is_superuser=False,
            role="user",
            created_at=fake.date_time_this_year(),
            updated_at=fake.date_time_this_month(),
        )

        # Apply overrides
        for key, value in overrides.items():
            if hasattr(user, key):
                setattr(user, key, value)

        return user

    @staticmethod
    def generate_applicant_data(**overrides) -> Applicant:
        """生成申请人数据 - 返回Applicant模型实例"""
        applicant = Applicant(
            id=uuid.uuid4(),
            user_id=overrides.get("user_id", uuid.uuid4()),  # 必需的外键
            surname=fake.last_name(),
            given_name=fake.first_name(),
            chinese_name=fake.name(),
            sex=fake.random_element(elements=("M", "F", "MALE", "FEMALE")),
            nationality=fake.random_element(elements=("China", "USA", "Canada")),
            date_of_birth=fake.date_of_birth(minimum_age=18, maximum_age=80),
            place_of_birth=fake.city(),
            passport_number=f"E{fake.random_number(digits=8)}",
            passport_type="P",
            date_of_issue=fake.date_between(start_date="-5y", end_date="today"),
            date_of_expiry=fake.date_between(start_date="today", end_date="+10y"),
            place_of_issue=fake.city(),
            telephone_number=fake.phone_number(),
            email=fake.email(),
            permanent_address=fake.address(),
            contact_address=fake.address(),
            work_unit=fake.company(),
            work_address=fake.address(),
            emergency_contact_name=fake.name(),
            emergency_contact_phone=fake.phone_number(),
            emergency_address=fake.address(),
            created_at=fake.date_time_this_year(),
            updated_at=fake.date_time_this_month(),
        )

        # Apply overrides
        for key, value in overrides.items():
            if hasattr(applicant, key):
                setattr(applicant, key, value)

        return applicant

    @staticmethod
    def generate_order_data(**overrides) -> Order:
        """生成订单数据 - 使用Faker生成真实数据"""
        order_date = fake.date_object()  # 修复：生成date对象而不是字符串
        return Order(
            id=uuid4(),
            user_id=overrides.get("user_id", uuid4()),
            order_no=overrides.get(
                "order_no",
                f"VN{order_date.strftime('%Y%m%d')}{fake.random_int(min=1000, max=9999)}",
            ),  # 修复：使用date对象的strftime
            order_status=overrides.get("order_status", "created"),
            order_type=overrides.get("order_type", "visa_application"),
            idempotent_key=overrides.get("idempotent_key", f"key_{fake.uuid4()}"),
            notes=overrides.get("notes", fake.text(max_nb_chars=200)),
            created_at=fake.date_time(),
            updated_at=fake.date_time(),
        )

    @staticmethod
    def generate_application_data(**overrides) -> Application:
        """生成申请数据 - 返回Application模型实例"""
        application = Application(
            id=uuid.uuid4(),
            user_id=overrides.get("user_id", uuid.uuid4()),  # 必需的外键
            order_id=overrides.get("order_id", uuid.uuid4()),  # 必需的外键
            applicant_id=overrides.get("applicant_id", uuid.uuid4()),  # 必需的外键
            country="VNM",
            category=overrides.get("category", "tourist"),
            visa_entry_type=overrides.get("visa_entry_type", "Single-entry"),
            visa_validity_duration=fake.random_element(
                elements=("30天", "90天", "365天")
            ),
            visa_start_date=fake.date_between(start_date="today", end_date="+3m"),
            intended_entry_gate=fake.random_element(
                elements=(
                    "Tan Son Nhat Int Airport (Ho Chi Minh City)",
                    "Noi Bai Int Airport (Hanoi)",
                    "Da Nang Int Airport",
                )
            ),
            purpose_of_entry=fake.random_element(
                elements=("Tourist", "Business", "Visit Family")
            ),
            visited_vietnam_last_year=fake.boolean(chance_of_getting_true=30),
            previous_entry_date=fake.date_between(start_date="-2y", end_date="-1y")
            if fake.boolean()
            else None,
            previous_exit_date=fake.date_between(start_date="-2y", end_date="-1y")
            if fake.boolean()
            else None,
            previous_purpose=fake.text(max_nb_chars=50) if fake.boolean() else None,
            has_vietnam_contact=fake.boolean(chance_of_getting_true=40),
            vietnam_contact_organization=fake.company() if fake.boolean() else None,
            vietnam_contact_phone=fake.phone_number() if fake.boolean() else None,
            vietnam_contact_address=fake.address() if fake.boolean() else None,
            vietnam_contact_purpose=fake.text(max_nb_chars=50)
            if fake.boolean()
            else None,
            customer_source=fake.random_element(
                elements=("website", "referral", "agent")
            ),
            expedited_type=fake.random_element(
                elements=("normal", "urgent", "super_urgent")
            )
            if fake.boolean()
            else None,
            form_snapshot={
                "test": "data",
                "generated_by": "ModernizedTestDataGenerator",
            },
            vietnam_application_number=f"VN{fake.random_number(digits=10)}"
            if fake.boolean()
            else None,
            created_at=fake.date_time_this_year(),
            updated_at=fake.date_time_this_month(),
        )

        # Apply overrides
        for key, value in overrides.items():
            if hasattr(application, key):
                setattr(application, key, value)

        return application

    @staticmethod
    def generate_visa_payment_data(**overrides) -> VisaPayment:
        """生成签证支付数据 - 使用Faker生成真实数据"""
        return VisaPayment(
            id=uuid4(),
            user_id=overrides.get("user_id", uuid4()),
            order_id=overrides.get("order_id", uuid4()),
            application_id=overrides.get("application_id", uuid4()),
            status=overrides.get("status", "created"),
            amount=overrides.get(
                "amount", fake.pydecimal(left_digits=2, right_digits=2, positive=True)
            ),
            currency=overrides.get("currency", "USD"),
            payment_channel=overrides.get(
                "payment_channel",
                fake.random_element(["credit_card", "bank_transfer", "paypal"]),
            ),
            created_at=fake.date_time(),
            updated_at=fake.date_time(),
            **{
                k: v
                for k, v in overrides.items()
                if k
                not in [
                    "user_id",
                    "order_id",
                    "application_id",
                    "status",
                    "amount",
                    "currency",
                    "payment_channel",
                ]
            },
        )

    @staticmethod
    def generate_file_data(**overrides) -> File:
        """生成文件数据 - 使用Faker生成真实数据"""
        return File(
            id=uuid4(),
            application_id=overrides.get("application_id", uuid4()),
            file_type=overrides.get("file_type", "passport"),
            file_url=fake.url() + f"/{fake.file_name(extension='jpg')}",
            file_name=fake.file_name(extension="jpg"),
            uploaded_at=fake.date_time(),
            created_at=fake.date_time(),
            updated_at=fake.date_time(),
            **{
                k: v
                for k, v in overrides.items()
                if k not in ["application_id", "file_type"]
            },
        )

    @staticmethod
    def generate_automation_logs_data(**overrides):
        """生成自动化日志数据 - 使用Faker生成真实数据"""
        # 注意：AutomationLogs模型没有在当前导入中，返回一个通用的日志对象
        from app.data.models.automation_logs import AutomationLogs

        return AutomationLogs(
            id=uuid4(),
            application_id=overrides.get("application_id", uuid4()),
            order_id=overrides.get("order_id", uuid4()),
            log_type=overrides.get(
                "log_type", fake.random_element(["info", "warning", "error"])
            ),
            message=overrides.get("message", fake.sentence()),
            details=overrides.get("details", fake.pydict()),
            created_at=fake.date_time(),
            **{
                k: v
                for k, v in overrides.items()
                if k
                not in ["application_id", "order_id", "log_type", "message", "details"]
            },
        )

    @staticmethod
    def create_placeholder_image(format="PNG", size=(1, 1), color="gray") -> bytes:
        """
        创建占位符图像 - 基于F Notepad的建议实现
        生成真实的、有效的图像数据，可以通过任何图像验证
        """
        image = Image.new("RGB", size, color=color)
        buffer = io.BytesIO()
        image.save(buffer, format=format)
        return buffer.getvalue()

    @staticmethod
    def create_passport_photo() -> bytes:
        """生成护照照片 - 100x120 JPEG格式"""
        return ModernizedTestDataGenerator.create_placeholder_image(
            "JPEG", (100, 120), "lightblue"
        )

    @staticmethod
    def create_portrait_photo() -> bytes:
        """生成肖像照片 - 120x160 JPEG格式"""
        return ModernizedTestDataGenerator.create_placeholder_image(
            "JPEG", (120, 160), "lightgray"
        )

    @staticmethod
    def create_document_scan() -> bytes:
        """生成文档扫描 - 200x150 PNG格式"""
        return ModernizedTestDataGenerator.create_placeholder_image(
            "PNG", (200, 150), "white"
        )


class TestDatabaseManager:
    """测试数据库管理器 - 负责创建和管理测试数据库连接"""

    def __init__(self):
        self.engine = None
        self.session_factory = None

    async def create_test_engine(self):
        """创建测试数据库引擎"""
        try:
            from app.data.base import Base

            database_url = "sqlite+aiosqlite:///:memory:"
            self.engine = create_async_engine(
                database_url,
                echo=False,
                pool_pre_ping=True,
                connect_args={"check_same_thread": False},
            )

            # 创建所有表
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)

            # 创建会话工厂
            self.session_factory = async_sessionmaker(
                bind=self.engine, class_=AsyncSession, expire_on_commit=False
            )

        except Exception as e:
            print(f"创建测试数据库失败: {e}")
            raise

    async def get_test_session(self) -> AsyncSession:
        """获取测试数据库会话"""
        if not self.session_factory:
            await self.create_test_engine()
        return self.session_factory()

    async def cleanup(self):
        """清理数据库连接"""
        if self.engine:
            await self.engine.dispose()


class MockPlaywrightPage:
    """Mock Playwright Page对象 - 用于测试浏览器自动化功能"""

    def __init__(self):
        self.url = ""
        self.title = "Test Page"
        self.content = "<html><body>Test Content</body></html>"

    def locator(self, selector: str) -> Mock:
        """返回Mock定位器对象"""
        mock_locator = Mock()
        mock_locator.click = AsyncMock()
        mock_locator.fill = AsyncMock()
        mock_locator.is_visible = AsyncMock(return_value=True)
        mock_locator.wait_for = AsyncMock()
        mock_locator.text_content = AsyncMock(return_value="Test Text")
        mock_locator.get_attribute = AsyncMock(return_value="test-value")
        return mock_locator

    def goto(self, url: str, timeout: int = 30000):
        """模拟页面导航"""
        self.url = url
        return AsyncMock()

    def wait_for_timeout(self, timeout: int):
        """模拟等待"""
        return AsyncMock()

    def screenshot(self, path: str | None = None) -> bytes:
        """模拟截图 - 返回真实的图像数据"""
        return ModernizedTestDataGenerator.create_placeholder_image(
            "PNG", (1280, 720), "white"
        )


class TestDataFactory:
    """
    传统测试数据工厂 - 现已现代化
    ====================================

    保持向后兼容性的同时，内部使用ModernizedTestDataGenerator
    提供现代化的数据生成能力，包括：
    - Faker生成的真实数据
    - Pillow生成的真实图像
    - SQLAlchemy模型实例返回

    迁移策略：
    1. 保持现有API不变
    2. 内部委托给ModernizedTestDataGenerator
    3. 逐步迁移到直接使用ModernizedTestDataGenerator
    """

    @staticmethod
    def create_user_data(override: dict[str, Any] | None = None) -> User:
        """创建用户测试数据 - 现代化版本"""
        return ModernizedTestDataGenerator.generate_user_data(**(override or {}))

    @staticmethod
    def create_applicant_data(override: dict[str, Any] | None = None) -> Applicant:
        """创建申请人测试数据 - 现代化版本"""
        return ModernizedTestDataGenerator.generate_applicant_data(**(override or {}))

    @staticmethod
    def create_application_data(override: dict[str, Any] | None = None) -> Application:
        """创建申请测试数据 - 现代化版本"""
        return ModernizedTestDataGenerator.generate_application_data(**(override or {}))

    @staticmethod
    def create_order_data(override: dict[str, Any] | None = None) -> Order:
        """创建订单测试数据 - 现代化版本"""
        return ModernizedTestDataGenerator.generate_order_data(**(override or {}))

    @staticmethod
    def create_visa_payment_data(override: dict[str, Any] | None = None) -> VisaPayment:
        """创建签证支付测试数据 - 现代化版本"""
        return ModernizedTestDataGenerator.generate_visa_payment_data(
            **(override or {})
        )

    @staticmethod
    def create_file_data(override: dict[str, Any] | None = None) -> File:
        """创建文件测试数据 - 现代化版本"""
        return ModernizedTestDataGenerator.generate_file_data(**(override or {}))

    @staticmethod
    def create_automation_logs_data(override: dict[str, Any] | None = None) -> Any:
        """创建自动化日志测试数据 - 现代化版本"""
        return ModernizedTestDataGenerator.generate_automation_logs_data(
            **(override or {})
        )


class TestConfigManager:
    """测试配置管理器"""

    @staticmethod
    def get_test_settings() -> dict[str, Any]:
        """获取测试配置"""
        return {
            "database_url": "sqlite+aiosqlite:///:memory:",
            "secret_key": "test_secret_key_for_testing",
            "environment": "testing",
            "browser": "chromium",
            "headless": True,
            "slow_mo": 0,
            "timeout": 30000,
            "screenshots_enabled": False,
            "debug_mode": False,
        }

    @staticmethod
    def get_mock_browser_config() -> dict[str, Any]:
        """获取Mock浏览器配置"""
        return {
            "timeout_ms": 30000,
            "screenshots_dir": "test_screenshots",
            "browser_type": "chromium",
            "viewport": {"width": 1280, "height": 720},
            "user_agent": "Mozilla/5.0 (Test Agent) AppleWebKit/537.36",
        }


# 全局测试数据库管理器实例
test_db_manager = TestDatabaseManager()


@pytest.fixture(scope="session")
async def test_db():
    """会话级别的测试数据库fixture"""
    await test_db_manager.create_test_engine()
    yield test_db_manager
    await test_db_manager.cleanup()


@pytest.fixture
async def db_session(test_db):
    """数据库会话fixture"""
    async with test_db.session_factory() as session:
        yield session
        await session.rollback()


@pytest.fixture
def mock_page():
    """Mock页面对象fixture"""
    return MockPlaywrightPage()


@pytest.fixture
def sample_user_data():
    """用户测试数据fixture - 现在返回模型实例"""
    return TestDataFactory.create_user_data()


@pytest.fixture
def sample_applicant_data():
    """申请人测试数据fixture - 现在返回模型实例"""
    return TestDataFactory.create_applicant_data()


@pytest.fixture
def sample_application_data():
    """申请测试数据fixture - 现在返回模型实例"""
    return TestDataFactory.create_application_data()


@pytest.fixture
def sample_order_data():
    """订单测试数据fixture - 现在返回模型实例"""
    return TestDataFactory.create_order_data()


@pytest.fixture
def sample_file_data():
    """文件测试数据fixture - 现在返回模型实例"""
    return TestDataFactory.create_file_data()


# 现代化数据生成器的fixtures
@pytest.fixture
def modern_user_data():
    """现代化用户数据fixture"""
    return ModernizedTestDataGenerator.generate_user_data()


@pytest.fixture
def modern_applicant_data():
    """现代化申请人数据fixture"""
    return ModernizedTestDataGenerator.generate_applicant_data()


@pytest.fixture
def modern_order_data():
    """现代化订单数据fixture"""
    return ModernizedTestDataGenerator.generate_order_data()


@pytest.fixture
def modern_application_data():
    """现代化申请数据fixture"""
    return ModernizedTestDataGenerator.generate_application_data()


# 真实图像数据的fixtures - 实现F Notepad建议
@pytest.fixture
def real_passport_photo():
    """真实护照照片数据fixture"""
    return ModernizedTestDataGenerator.create_passport_photo()


@pytest.fixture
def real_portrait_photo():
    """真实肖像照片数据fixture"""
    return ModernizedTestDataGenerator.create_portrait_photo()


@pytest.fixture
def real_document_scan():
    """真实文档扫描数据fixture"""
    return ModernizedTestDataGenerator.create_document_scan()


@pytest.fixture
def test_config():
    """测试配置fixture"""
    return TestConfigManager.get_test_settings()


@pytest.fixture
def mock_browser_config():
    """Mock浏览器配置fixture"""
    return TestConfigManager.get_mock_browser_config()
