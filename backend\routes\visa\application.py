# backend/routes/visa/application.py
"""
签证申请提交功能模块

遵循PP和QQ Notepad要求：
- 单一职责：专注于申请提交逻辑
- API兼容性：保持现有接口完全兼容
- 异常处理：全链路日志与异常兜底
- 异步处理：文件上传和自动化任务
"""

from datetime import UTC, date, datetime
from pathlib import Path
import uuid

from fastapi import (
    APIRouter,
    BackgroundTasks,
    Depends,
    File,
    Form,
    HTTPException,
    UploadFile,
)

from app.utils.logger_config import get_logger
from backend.api.schemas.order import CreateOrderRequest
from backend.auth_fastapi_users.dependencies import require_auth
from backend.core.exceptions import VisaApplicationError

# 修复导入路径 - 使用绝对导入避免依赖陷阱
from backend.routes.visa.schemas import VisaApplicationResponse

logger = get_logger()

router = APIRouter()


# 辅助函数 - 提取方法重构
async def _get_task_info(order_no: str) -> tuple[str, str]:
    """获取任务信息 - 单一职责原则"""
    try:
        from sqlalchemy import desc, select

        from app.data.models.automation_logs import AutomationLogs
        from app.data.models.order import Order
        from backend.auth_fastapi_users.database import get_async_session

        logger.info(f"🔍 查询任务信息: order_no={order_no}")

        async with get_async_session() as session:
            # 先查找订单是否存在
            order = await session.scalar(
                select(Order).where(Order.order_no == order_no)
            )

            if not order:
                logger.warning(f"❌ 订单不存在: {order_no}")
                return None, None  # type: ignore[return-value]

            logger.info(f"✅ 找到订单: order_id={order.id}, order_no={order.order_no}")

            # 查找automation_logs记录
            log = await session.scalar(
                select(AutomationLogs)
                .where(AutomationLogs.order_id == order.id)
                .order_by(desc(AutomationLogs.created_at))
            )

            if not log:
                logger.warning(f"❌ 未找到automation_logs记录: order_id={order.id}")
                return None, None  # type: ignore[return-value]

            logger.info(
                f"✅ 找到任务记录: celery_task_id={log.celery_task_id}, status={log.task_status}"
            )
            return (log.celery_task_id, log.task_status)  # type: ignore[return-value]

    except Exception as e:
        logger.error(f"❌ 查询任务信息失败: {e}", exc_info=True)
        return None, None  # type: ignore[return-value]


def _revoke_celery_task(task_id: str):
    """取消Celery任务 - 封装实现细节"""
    from celery_worker.celery_app import app

    app.control.revoke(task_id, terminate=True, signal="SIGKILL")


def _parse_validity_days(validity_duration: str) -> int:
    """解析签证有效期天数"""
    if not validity_duration:
        return 30

    # 提取数字
    import re

    numbers = re.findall(r"\d+", validity_duration)
    if numbers:
        return int(numbers[0])
    return 30


@router.post("/apply", response_model=VisaApplicationResponse)
async def apply_visa(
    portrait_photo: UploadFile = File(..., description="Portrait photo (JPG/PNG)"),
    passport_scan: UploadFile = File(..., description="Passport scan (JPG/PNG)"),
    # 所有表单字段作为Form参数（保持完全兼容）
    surname: str | None = Form(None),
    given_name: str | None = Form(None),
    chinese_name: str | None = Form(None),
    sex: str = Form(...),
    dob: str | None = Form(None),
    place_of_birth: str | None = Form(None),
    nationality: str | None = Form("CHINA"),
    religion: str | None = Form("NO"),
    passport_number: str | None = Form(None),
    passport_type: str | None = Form("Ordinary passport"),
    place_of_issue: str | None = Form(None),
    date_of_issue: str | None = Form(None),
    passport_expiry: str | None = Form(None),
    email: str | None = Form(None),
    telephone_number: str | None = Form(None),
    permanent_address: str | None = Form(None),
    contact_address: str | None = Form(None),
    emergency_contact_name: str | None = Form(None),
    emergency_address: str | None = Form(None),
    emergency_contact_phone: str | None = Form(None),
    visa_entry_type: str = Form(...),
    visa_validity_duration: str = Form(...),
    visa_start_date: str = Form(...),
    intended_entry_gate: str = Form(...),
    purpose_of_entry: str = Form("Tourist"),
    expedited_type: str | None = Form(None),
    visited_vietnam_last_year: bool = Form(False),
    previous_entry_date: str | None = Form(None),
    previous_exit_date: str | None = Form(None),
    previous_purpose: str | None = Form(None),
    # Vietnam contact fields
    has_vietnam_contact: bool = Form(False),
    vietnam_contact_organization: str | None = Form(None),
    vietnam_contact_phone: str | None = Form(None),
    vietnam_contact_address: str | None = Form(None),
    vietnam_contact_purpose: str | None = Form(None),
    # 客户信息字段
    customer_source: str | None = Form(None),
    # 控制字段
    force_resubmit: str | None = Form(None),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    user=Depends(require_auth),
):
    """
    提交签证申请（包含文件上传）

    - **form_data**: 签证申请表单数据
    - **portrait_photo**: 证件照片文件
    - **passport_scan**: 护照扫描文件
    - **返回**: 申请结果和跟踪信息
    """
    request_id = str(uuid.uuid4())
    logger.info(f"⚡ [{request_id}] Received /api/visa/apply request")

    # 初始化变量
    order_no = None  # 订单编号
    temp_photo_path: Path | None = None
    temp_passport_path: Path | None = None
    created_temp_files: list[Path | None] = []

    # 添加文件上传调试日志
    logger.info(f"🔍 DEBUG: portrait_photo received = {portrait_photo is not None}")
    logger.info(f"🔍 DEBUG: passport_scan received = {passport_scan is not None}")
    if portrait_photo:
        logger.info(f"🔍 DEBUG: portrait_photo filename = '{portrait_photo.filename}'")
        logger.info(
            f"🔍 DEBUG: portrait_photo content_type = '{portrait_photo.content_type}'"
        )
    if passport_scan:
        logger.info(f"🔍 DEBUG: passport_scan filename = '{passport_scan.filename}'")
        logger.info(
            f"🔍 DEBUG: passport_scan content_type = '{passport_scan.content_type}'"
        )

    # 🔥 新增：检查force_resubmit标识
    is_force_resubmit = force_resubmit == "true"
    logger.info(f"[{request_id}] force_resubmit标识: {is_force_resubmit}")

    # 🔥 新增：业务级重复检查 - 在创建订单之前进行
    if not is_force_resubmit and passport_number:
        logger.info(f"[{request_id}] 开始业务级重复检查...")
        try:
            # 导入重复检查函数
            from backend.routes.visa.file_handling import check_duplicate_submission

            # 调用重复检查
            duplicate_result = await check_duplicate_submission(passport_number)

            if duplicate_result.get("exists") and not duplicate_result.get(
                "can_resubmit", True
            ):
                # 检测到重复且不允许直接重新提交，返回重复提示
                logger.info(
                    f"[{request_id}] 检测到业务级重复提交，需要用户确认: {passport_number}"
                )
                return VisaApplicationResponse(
                    success=False,
                    message=f"护照号 {passport_number} 之前已提交过申请，请确认是否重新提交",
                    application_id=None,
                    status="duplicate_detected",
                    tracking_info={  # type: ignore[arg-type]
                        "submission_time": duplicate_result.get(
                            "submission_time", "未知"
                        ),
                        "expected_processing_days": "3-5",
                        "email": email,
                        "order_no": duplicate_result.get("order_no"),
                        "warning_type": duplicate_result.get("warning_type"),
                        "requires_confirmation": True,
                    },
                )
            else:
                logger.info(f"[{request_id}] 业务级重复检查通过，可以继续提交")
        except Exception as e:
            logger.warning(f"[{request_id}] 业务级重复检查失败，继续提交流程: {str(e)}")
    elif is_force_resubmit:
        logger.info(f"[{request_id}] 用户已确认强制重新提交，跳过重复检查")
    else:
        logger.info(f"[{request_id}] 无护照号或其他原因，跳过重复检查")

    # 🔥 新增：创建订单编号 - 在文件处理之前先生成订单
    logger.info(f"[{request_id}] 开始生成订单编号...")
    try:
        # 使用正确的依赖注入方式
        from app.repositories.order_repository import OrderRepository
        from app.services.order_service import OrderService
        from backend.auth_fastapi_users.database import get_async_session

        async with get_async_session() as session:
            order_repository = OrderRepository(session)
            service = OrderService(order_repository)
            await service.initialize()

        # 构建申请人姓名 - 处理中英文姓名
        applicant_name = f"{surname or ''} {given_name or ''}".strip()
        if chinese_name:
            applicant_name = f"{applicant_name} ({chinese_name})"

        # 解析出生日期
        try:
            if dob:
                # 支持多种日期格式：DD/MM/YYYY, YYYY-MM-DD
                if "/" in dob:
                    # DD/MM/YYYY format
                    day, month, year = dob.split("/")
                    birth_date = date(int(year), int(month), int(day))
                else:
                    # YYYY-MM-DD format
                    birth_date = date.fromisoformat(dob)
            else:
                # 默认日期 - 如果没有提供出生日期
                birth_date = date(1990, 1, 1)
        except (ValueError, TypeError):
            logger.warning(f"[{request_id}] 出生日期解析失败: {dob}, 使用默认日期")
            birth_date = date(1990, 1, 1)

        # 准备完整的申请数据
        application_data = {
            "form_data": {
                "surname": surname,
                "given_name": given_name,
                "chinese_name": chinese_name,
                "sex": sex,
                "dob": dob,
                "place_of_birth": place_of_birth,
                "nationality": nationality or "CHINA",
                "religion": religion or "NO",
                "passport_number": passport_number,
                "passport_type": passport_type or "Ordinary passport",
                "place_of_issue": place_of_issue,
                "date_of_issue": date_of_issue,
                "passport_expiry": passport_expiry,
                "email": email,
                "telephone_number": telephone_number,
                "permanent_address": permanent_address,
                "contact_address": contact_address,
                "emergency_contact_name": emergency_contact_name,
                "emergency_address": emergency_address,
                "emergency_contact_phone": emergency_contact_phone,
                "visa_entry_type": visa_entry_type,
                "visa_validity_duration": visa_validity_duration,
                "visa_start_date": visa_start_date,
                "intended_entry_gate": intended_entry_gate,
                "purpose_of_entry": purpose_of_entry,
                "expedited_type": expedited_type,
                "visited_vietnam_last_year": visited_vietnam_last_year,
                "previous_entry_date": previous_entry_date,
                "previous_exit_date": previous_exit_date,
                "previous_purpose": previous_purpose,
                # Vietnam contact fields
                "has_vietnam_contact": has_vietnam_contact,
                "vietnam_contact_organization": vietnam_contact_organization,
                "vietnam_contact_phone": vietnam_contact_phone,
                "vietnam_contact_address": vietnam_contact_address,
                "vietnam_contact_purpose": vietnam_contact_purpose,
                # 客户信息字段
                "customer_source": customer_source,
                # 控制字段
                "force_resubmit": force_resubmit,
            },
            "file_info": {
                "portrait_photo_filename": portrait_photo.filename
                if portrait_photo
                else None,
                "passport_scan_filename": passport_scan.filename
                if passport_scan
                else None,
                "portrait_photo_content_type": portrait_photo.content_type
                if portrait_photo
                else None,
                "passport_scan_content_type": passport_scan.content_type
                if passport_scan
                else None,
                # 🔥 修复：初始化为空字符串而不是None，避免Celery序列化问题
                "portrait_photo_path": "",
                "passport_scan_path": "",
            },
            "request_metadata": {
                "request_id": request_id,
                "created_at": datetime.now(UTC).isoformat(),
                "api_version": "v1",
            },
        }

        # 创建订单请求
        order_request = CreateOrderRequest(
            applicant_name=applicant_name,
            passport_number=passport_number.strip() if passport_number else "",
            date_of_birth=birth_date,
            application_data=application_data,
        )

        # 调用订单服务创建订单
        order_result = await service.create_order(
            user_id=user.id, request=order_request
        )

        if not order_result["success"]:
            raise VisaApplicationError(
                f"订单创建失败: {order_result['message']}", "ORDER_CREATION_FAILED"
            )

        order_data = order_result["data"]
        order_no = order_data["order_no"]
        is_duplicate_order = order_data.get("is_duplicate", False)

        if is_duplicate_order:
            logger.info(f"[{request_id}] 检测到重复订单: {order_no}")
            return VisaApplicationResponse(
                success=True,
                message="检测到重复提交，系统已跳过处理。您的申请之前已成功提交。",
                application_id=order_no,
                status="duplicate",
                tracking_info={  # type: ignore[arg-type]
                    "submission_time": "之前已提交",
                    "expected_processing_days": "3-5",
                    "email": email,
                    "is_duplicate": True,
                    "order_no": order_no,
                },
            )

        logger.info(f"[{request_id}] ✅ 订单创建成功: {order_no}")

    except Exception as e:
        logger.error(f"[{request_id}] ❌ 订单创建失败: {str(e)}")
        raise VisaApplicationError(f"订单创建失败: {str(e)}", "ORDER_CREATION_FAILED")

    try:
        # 1. 处理文件上传 - 对重试请求复用存储的文件
        logger.info(f"[{request_id}] Processing uploaded files...")

        # 处理证件照片 - 创建在共享app目录中的临时文件
        if portrait_photo:
            # 确保temp目录存在
            temp_dir = Path("/app/temp")
            temp_dir.mkdir(exist_ok=True)
            temp_photo_path = temp_dir / f"portrait_api_{uuid.uuid4().hex[:8]}.jpg"
            logger.info(
                f"🔍 DEBUG: Creating portrait photo temp file: {temp_photo_path}"
            )
            with open(temp_photo_path, "wb") as temp_file:
                content = await portrait_photo.read()
                temp_file.write(content)
                logger.info(
                    f"🔍 DEBUG: Portrait photo content size: {len(content)} bytes"
                )
            if temp_photo_path:
                created_temp_files.append(temp_photo_path)
                application_data["file_info"]["portrait_photo_path"] = str(  # type: ignore[index]
                    temp_photo_path
                )
                logger.info(f"[{request_id}] Portrait photo saved to {temp_photo_path}")
        else:
            raise HTTPException(status_code=422, detail="Portrait photo is required.")

        # 处理护照扫描 - 创建在共享app目录中的临时文件
        if passport_scan:
            # 确保temp目录存在
            temp_dir = Path("/app/temp")
            temp_dir.mkdir(exist_ok=True)
            temp_passport_path = temp_dir / f"passport_api_{uuid.uuid4().hex[:8]}.jpg"
            logger.info(
                f"🔍 DEBUG: Creating passport scan temp file: {temp_passport_path}"
            )
            with open(temp_passport_path, "wb") as temp_file:
                content = await passport_scan.read()
                temp_file.write(content)
                logger.info(
                    f"🔍 DEBUG: Passport scan content size: {len(content)} bytes"
                )
            if temp_passport_path:
                created_temp_files.append(temp_passport_path)
                application_data["file_info"]["passport_scan_path"] = str(  # type: ignore[index]
                    temp_passport_path
                )
                logger.info(
                    f"[{request_id}] Passport scan saved to {temp_passport_path}"
                )
        else:
            raise HTTPException(status_code=422, detail="Passport scan is required.")

        # 3. 创建Application记录（支持取消功能）
        logger.info(f"[{request_id}] 创建Application记录...")
        try:
            from app.data.models.application import Application

            async with get_async_session() as session:
                # 获取order_id
                from sqlalchemy import select

                from app.data.models.order import Order

                order_query = select(Order.id).where(Order.order_no == order_no)
                order_result = await session.execute(order_query)  # type: ignore[assignment]
                order_id = order_result.scalar_one()  # type: ignore[attr-defined]

                # 1. 创建或复用Applicant记录（基本信息）
                from app.data.models.applicant import Applicant

                # 处理日期字段
                def parse_date_field(date_str):
                    if not date_str:
                        return None
                    try:
                        if "/" in date_str:
                            day, month, year = date_str.split("/")
                            return date(int(year), int(month), int(day))
                        else:
                            return date.fromisoformat(date_str)
                    except (ValueError, TypeError):
                        return None

                # 处理签证开始日期
                visa_start_date_obj = parse_date_field(visa_start_date)

                # 检查是否已存在相同的申请人记录（基于用户ID和护照号）
                existing_applicant_query = select(Applicant).where(
                    Applicant.user_id == user.id,
                    Applicant.passport_number == passport_number,
                )
                existing_applicant_result = await session.execute(
                    existing_applicant_query
                )
                applicant = existing_applicant_result.scalar_one_or_none()

                if applicant:
                    # 更新现有申请人的基本信息（可能有变化的信息）
                    logger.info(f"[{request_id}] 复用现有申请人记录: {applicant.id}")
                    # 只更新可能变化的基本信息
                    applicant.surname = surname  # type: ignore[assignment]
                    applicant.given_name = given_name  # type: ignore[assignment]
                    applicant.chinese_name = chinese_name  # type: ignore[assignment]
                    applicant.sex = sex  # type: ignore[assignment]
                    applicant.nationality = nationality or "CHINA"  # type: ignore[assignment]
                    applicant.date_of_birth = birth_date  # type: ignore[assignment]
                    applicant.place_of_birth = place_of_birth  # type: ignore[assignment]
                    applicant.telephone_number = telephone_number  # type: ignore[assignment]
                    applicant.email = email  # type: ignore[assignment]
                    applicant.permanent_address = permanent_address  # type: ignore[assignment]
                    applicant.contact_address = contact_address  # type: ignore[assignment]
                    applicant.date_of_issue = parse_date_field(date_of_issue)  # type: ignore[assignment]
                    applicant.date_of_expiry = parse_date_field(passport_expiry)  # type: ignore[assignment]
                    applicant.place_of_issue = place_of_issue  # type: ignore[assignment]
                    applicant.work_unit = getattr(  # type: ignore[assignment]
                        applicant, "work_unit", None
                    )  # 保持现有值
                    applicant.work_address = getattr(  # type: ignore[assignment]
                        applicant, "work_address", None
                    )  # 保持现有值
                    applicant.emergency_contact_name = emergency_contact_name  # type: ignore[assignment]
                    applicant.emergency_contact_phone = emergency_contact_phone  # type: ignore[assignment]
                    applicant.emergency_address = emergency_address  # type: ignore[assignment]

                else:
                    # 创建新的申请人记录（只包含基本信息）
                    logger.info(f"[{request_id}] 创建新申请人记录")
                    applicant = Applicant(
                        id=uuid.uuid4(),
                        user_id=user.id,
                        # 基本身份信息
                        surname=surname,
                        given_name=given_name,
                        chinese_name=chinese_name,
                        sex=sex,
                        nationality=nationality or "CHINA",
                        date_of_birth=birth_date,
                        place_of_birth=place_of_birth,
                        # 护照信息
                        passport_number=passport_number,
                        passport_type=passport_type or "Ordinary passport",
                        date_of_issue=parse_date_field(date_of_issue),
                        date_of_expiry=parse_date_field(passport_expiry),
                        place_of_issue=place_of_issue,
                        # 联系方式
                        telephone_number=telephone_number,
                        email=email,
                        permanent_address=permanent_address,
                        contact_address=contact_address,
                        # 紧急联系人
                        emergency_contact_name=emergency_contact_name,
                        emergency_contact_phone=emergency_contact_phone,
                        emergency_address=emergency_address,
                    )
                    session.add(applicant)

                await session.flush()  # 获取applicant.id

                # 2. 创建Application记录（包含具体申请信息）
                application = Application(
                    id=uuid.uuid4(),
                    user_id=user.id,  # 🔧 修复：添加缺失的user_id字段
                    order_id=order_id,
                    applicant_id=applicant.id,
                    # 签证配置（替代visa_type表）
                    country="VNM",  # Vietnam
                    category="tourist",  # 默认旅游签证
                    form_snapshot=application_data,
                    # 具体申请信息（从applicant表移入）
                    visa_entry_type=visa_entry_type,
                    visa_validity_duration=visa_validity_duration,
                    visa_start_date=visa_start_date_obj,
                    intended_entry_gate=intended_entry_gate,
                    purpose_of_entry=purpose_of_entry,
                    # 既往越南记录
                    visited_vietnam_last_year=visited_vietnam_last_year,
                    previous_entry_date=parse_date_field(previous_entry_date),
                    previous_exit_date=parse_date_field(previous_exit_date),
                    previous_purpose=previous_purpose,
                    # 越南联系人/组织
                    has_vietnam_contact=has_vietnam_contact,
                    vietnam_contact_organization=vietnam_contact_organization,
                    vietnam_contact_phone=vietnam_contact_phone,
                    vietnam_contact_address=vietnam_contact_address,
                    vietnam_contact_purpose=vietnam_contact_purpose,
                    # 客户信息
                    customer_source=customer_source,
                    # 加急类型
                    expedited_type=expedited_type,
                )
                session.add(application)
                await session.commit()

                logger.info(f"[{request_id}] ✅ 申请记录创建成功:")
                logger.info(f"[{request_id}]   - 申请人ID: {applicant.id}")
                logger.info(f"[{request_id}]   - 申请ID: {application.id}")

        except Exception as app_error:
            logger.error(f"[{request_id}] ❌ Application记录创建失败: {app_error}")
            # 🔥 修复：Application记录创建失败应该阻断流程
            raise VisaApplicationError(
                f"Application记录创建失败: {str(app_error)}",
                "APPLICATION_CREATION_FAILED",
            )

        # 4. 提交自动化任务到Celery（异步处理）
        logger.info(f"[{request_id}] 提交自动化任务到Celery队列...")

        # 导入Celery应用
        from celery_worker.celery_app import app as celery_app

        # 将任务提交到Celery
        task = celery_app.send_task(
            "celery_worker.tasks.process_visa_application",
            args=[order_no, str(user.id), application_data],
        )

        logger.info(f"[{request_id}] ✅ 任务已提交到Celery，Celery_task_id: {task.id}")

        # ✅ automation_logs记录将由Celery worker通过HTTP API通知FastAPI创建
        # 🔥 新架构：FastAPI统一负责数据库操作，Celery通过HTTP通知状态变更
        logger.info(
            f"[{request_id}] ✅ 任务已提交到Celery队列celery_task_id: {task.id}"
        )

        # 立即返回响应，不等待自动化完成
        return VisaApplicationResponse(
            success=True,
            message="申请已成功提交！系统正在后台处理您的申请，请稍后查看处理结果。",
            application_id=order_no,
            status="processing",
            tracking_info={  # type: ignore[arg-type]
                "submission_time": "刚刚",
                "expected_processing_days": "3-5",
                "email": email,
                "order_no": order_no,
                "celery_task_id": task.id,
                "processing_status": "已提交到异步队列",
            },
        )

    except Exception as e:
        # 🔥 异常处理：记录详细错误信息
        error_msg = str(e).replace("{", "{{").replace("}", "}}")
        logger.error(f"[{request_id}] ❌ 申请处理异常: {error_msg}", exc_info=True)

    finally:
        # 5. 文件清理由Celery worker负责
        # 不在这里清理文件，因为Celery worker还需要使用这些文件
        logger.info(f"[{request_id}] File cleanup will be handled by Celery worker")


@router.post("/cancel")
async def cancel_application(request: dict, user=Depends(require_auth)):
    """取消申请"""
    order_no = request.get("order_no")

    logger.info(f"🔍 收到取消申请请求订单编号: {order_no}")

    # Guard Clauses - 早期返回模式
    if not order_no:
        raise HTTPException(400, "❌取消申请失败，缺少订单编号")
    if not order_no.startswith("VN"):
        return {"success": False, "message": "❌取消申请失败，无效订单编号"}

    # 获取任务状态 - 单一职责
    task_id, task_status = await _get_task_info(order_no)
    if not task_id:
        return {
            "success": False,
            "message": f"❌取消失败: 订单编号 {order_no} 未找到对应的自动化任务",
        }

    # 🔥 完善：针对不同状态给出更友好的提示
    if task_status == "success":
        return {"success": False, "message": "❌取消失败: 该申请已成功提交无法取消。"}
    elif task_status == "failed":
        return {"success": False, "message": "❌取消失败: 该申请提交失败，无需取消。"}
    elif task_status == "cancelled":
        return {
            "success": False,
            "message": "❌取消失败: 该申请已经被取消，请不要重复取消。",
        }
    elif task_status == "processing":
        logger.info(f"✅ 发起取消申请 celery_task_id: {task_id}")

    # 执行取消 - 简洁明了
    _revoke_celery_task(task_id)

    # ✅ 不在FastAPI中更新automation_logs状态，避免事件循环冲突
    logger.info(f"✅ 任务已取消 celery_task_id: {task_id}")
    return {"success": True, "message": "申请已取消"}
